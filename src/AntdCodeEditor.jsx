import React, { useState, useRef, useEffect } from "react";
import { Input } from "antd";
const { TextArea } = Input;

// --- Token Data ---
const KEYWORDS = [
  "const",
  "let",
  "var",
  "function",
  "return",
  "if",
  "else",
  "for",
  "while",
  "do",
  "switch",
  "case",
  "break",
  "continue",
  "new",
  "try",
  "catch",
  "finally",
  "throw",
  "typeof",
  "instanceof",
  "void",
  "delete",
  "in",
  "of",
  "this",
  "super",
  "class",
  "extends",
  "import",
  "export",
  "default",
  "async",
  "await",
  "yield",
];
const LITERALS = ["true", "false", "null", "undefined"];
const BUILT_INS = ["console", "Math", "Date", "Array", "Object", "String"];

const tokenStyles = {
  keyword: { color: "#d73a49", fontWeight: "bold" },
  literal: { color: "#005cc5" },
  builtin: { color: "#6f42c1" },
  string: { color: "#032f62" },
  number: { color: "#005cc5" },
  operator: { color: "#d73a49" },
  identifier: { color: "#24292e" },
  punctuation: { color: "#24292e" },
  comment: { color: "#6a737d", fontStyle: "italic" },
  whitespace: {},
  plain: {},
};

function tokenize(code) {
  const regex =
    /\s+|\/\/.*|\/\*[\s\S]*?\*\/|"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\d+(?:\.\d+)?|[a-zA-Z_$][a-zA-Z0-9_$]*|=>|==|!=|<=|>=|[\+\-\*\/=<>!&|]+|[{}()[\];,]/g;

  const tokens = [];
  let match;

  while ((match = regex.exec(code)) !== null) {
    const value = match[0];
    let type = "plain";

    if (/^\s+$/.test(value)) type = "whitespace";
    else if (/^\/\/|\/\*/.test(value)) type = "comment";
    else if (/^['"]/.test(value)) type = "string";
    else if (/^\d/.test(value)) type = "number";
    else if (LITERALS.includes(value)) type = "literal";
    else if (KEYWORDS.includes(value)) type = "keyword";
    else if (BUILT_INS.includes(value)) type = "builtin";
    else if (/^[{}()[\];,]$/.test(value)) type = "punctuation";
    else if (/^[+\-*/=<>!&|]+$/.test(value)) type = "operator";
    else if (/^[a-zA-Z_$]/.test(value)) type = "identifier";

    tokens.push({ type, value });
  }

  return tokens;
}

// --- Main Component ---
export default function AntdCodeEditor({ initialCode = "" }) {
  const [code, setCode] = useState(initialCode);
  const preRef = useRef(null);
  const textareaRef = useRef(null);

  // Sync scroll position
  const syncScroll = () => {
    if (preRef.current && textareaRef.current?.resizableTextArea?.textArea) {
      const ta = textareaRef.current.resizableTextArea.textArea;
      preRef.current.scrollTop = ta.scrollTop;
      preRef.current.scrollLeft = ta.scrollLeft;
    }
  };

  useEffect(syncScroll, [code]);

  // Clean and normalize the code before tokenizing
  const normalizedCode = code.replace(/[\uFEFF\uFFFD]/g, '');
  const tokens = tokenize(normalizedCode);

  return (
    <div style={{ position: "relative", fontFamily: "monospace" }}>
      <pre
        ref={preRef}
        aria-hidden="true"
        style={{
          pointerEvents: "none",
          margin: 0,
          position: "absolute",
          top: 0,
          left: 0,
          padding: 12,
          whiteSpace: "pre-wrap",
          wordWrap: "break-word",
          width: "100%",
          minHeight: 120,
          color: "transparent",
          backgroundColor: "#f6f8fa",
          overflow: "hidden",
          borderRadius: 6,
        }}
      >
        {tokens.map((token, i) => (
          <span
            key={i}
            style={{
              ...tokenStyles[token.type],
              whiteSpace: token.type === "whitespace" ? "pre" : "pre-wrap",
            }}
          >
            {token.value}
          </span>
        ))}
      </pre>

      <TextArea
        ref={textareaRef}
        value={code}
        onChange={(e) => setCode(e.target.value)}
        onScroll={syncScroll}
        autoSize={{ minRows: 6 }}
        spellCheck={false}
        style={{
          fontFamily: "monospace",
          fontSize: 14,
          padding: 12,
          borderRadius: 6,
          backgroundColor: "transparent",
          color: "#000",
          width: "100%",
          position: "relative",
          zIndex: 1,
          overflow: "hidden",
        }}
      />
    </div>
  );
}
