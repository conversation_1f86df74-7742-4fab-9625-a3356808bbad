import { useState, useRef, useEffect } from "react";
import { Input } from "antd";
const { TextArea } = Input;

// --- Language Definitions ---
const LANGUAGES = {
  javascript: {
    keywords: [
      "const",
      "let",
      "var",
      "function",
      "return",
      "if",
      "else",
      "for",
      "while",
      "do",
      "switch",
      "case",
      "break",
      "continue",
      "new",
      "try",
      "catch",
      "finally",
      "throw",
      "typeof",
      "instanceof",
      "void",
      "delete",
      "in",
      "of",
      "this",
      "super",
      "class",
      "extends",
      "import",
      "export",
      "default",
      "async",
      "await",
      "yield",
    ],
    literals: ["true", "false", "null", "undefined"],
    builtins: ["console", "Math", "Date", "Array", "Object", "String"],
    regex:
      /\s+|\/\/.*|\/\*[\s\S]*?\*\/|"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\d+(?:\.\d+)?|[a-zA-Z_$][a-zA-Z0-9_$]*|=>|==|!=|<=|>=|[\+\-\*\/=<>!&|]+|[{}()[\];,]/g,
  },
  css: {
    keywords: [
      "important",
      "inherit",
      "initial",
      "unset",
      "auto",
      "none",
      "normal",
      "bold",
      "italic",
      "underline",
      "overline",
      "line-through",
      "left",
      "right",
      "center",
      "justify",
      "top",
      "bottom",
      "middle",
      "baseline",
      "absolute",
      "relative",
      "fixed",
      "static",
      "sticky",
      "block",
      "inline",
      "inline-block",
      "flex",
      "grid",
      "table",
      "hidden",
      "visible",
      "scroll",
      "auto",
      "transparent",
      "solid",
      "dashed",
      "dotted",
      "double",
    ],
    literals: ["true", "false"],
    builtins: [
      "px",
      "em",
      "rem",
      "vh",
      "vw",
      "vmin",
      "vmax",
      "%",
      "pt",
      "pc",
      "in",
      "cm",
      "mm",
      "deg",
      "rad",
      "turn",
      "s",
      "ms",
      "Hz",
      "kHz",
      "dpi",
      "dpcm",
      "dppx",
    ],
    properties: [
      "color",
      "background",
      "background-color",
      "background-image",
      "background-size",
      "background-position",
      "background-repeat",
      "border",
      "border-color",
      "border-width",
      "border-style",
      "border-radius",
      "margin",
      "padding",
      "width",
      "height",
      "max-width",
      "max-height",
      "min-width",
      "min-height",
      "display",
      "position",
      "top",
      "right",
      "bottom",
      "left",
      "z-index",
      "overflow",
      "float",
      "clear",
      "font-family",
      "font-size",
      "font-weight",
      "font-style",
      "text-align",
      "text-decoration",
      "line-height",
      "letter-spacing",
      "word-spacing",
      "text-transform",
      "white-space",
      "opacity",
      "visibility",
      "cursor",
      "pointer-events",
      "user-select",
      "box-sizing",
      "flex",
      "flex-direction",
      "justify-content",
      "align-items",
      "grid",
      "grid-template-columns",
      "grid-template-rows",
      "gap",
      "transform",
      "transition",
      "animation",
    ],
    regex:
      /\s+|\/\*[\s\S]*?\*\/|"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|#[0-9a-fA-F]{3,8}|rgba?\([^)]*\)|hsla?\([^)]*\)|\d+(?:\.\d+)?(?:px|em|rem|vh|vw|vmin|vmax|%|pt|pc|in|cm|mm|deg|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?|[a-zA-Z-]+|[{}();:,!]/g,
  },
};

const tokenStyles = {
  keyword: { color: "#d73a49", fontWeight: "bold" },
  literal: { color: "#005cc5" },
  builtin: { color: "#6f42c1" },
  string: { color: "#032f62" },
  number: { color: "#005cc5" },
  operator: { color: "#d73a49" },
  identifier: { color: "#24292e" },
  punctuation: { color: "#24292e" },
  comment: { color: "#6a737d", fontStyle: "italic" },
  whitespace: {},
  plain: { color: "#24292e" },
  // CSS-specific token types
  property: { color: "#005cc5", fontWeight: "bold" },
  value: { color: "#032f62" },
  selector: { color: "#6f42c1" },
  unit: { color: "#d73a49" },
  color: { color: "#e36209" },
};

function tokenize(code, language = "javascript") {
  const lang = LANGUAGES[language] || LANGUAGES.javascript;
  const regex = lang.regex;
  const tokens = [];
  let match;

  while ((match = regex.exec(code)) !== null) {
    const value = match[0];
    let type = "plain";

    if (/^\s+$/.test(value)) {
      type = "whitespace";
    } else if (/^\/\/|\/\*/.test(value)) {
      type = "comment";
    } else if (/^['"]/.test(value)) {
      type = "string";
    } else if (language === "css") {
      // CSS-specific tokenization
      if (
        /^#[0-9a-fA-F]{3,8}$/.test(value) ||
        /^rgba?\(/.test(value) ||
        /^hsla?\(/.test(value)
      ) {
        type = "color";
      } else if (
        /^\d+(?:\.\d+)?(?:px|em|rem|vh|vw|vmin|vmax|%|pt|pc|in|cm|mm|deg|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)$/.test(
          value
        )
      ) {
        type = "number";
      } else if (/^\d+(?:\.\d+)?$/.test(value)) {
        type = "number";
      } else if (lang.properties && lang.properties.includes(value)) {
        type = "property";
      } else if (lang.keywords.includes(value)) {
        type = "keyword";
      } else if (lang.builtins.includes(value)) {
        type = "builtin";
      } else if (/^[{}();:,!]$/.test(value)) {
        type = "punctuation";
      } else if (/^[a-zA-Z-]+$/.test(value)) {
        type = "identifier";
      }
    } else {
      // JavaScript tokenization
      if (/^\d/.test(value)) {
        type = "number";
      } else if (lang.literals.includes(value)) {
        type = "literal";
      } else if (lang.keywords.includes(value)) {
        type = "keyword";
      } else if (lang.builtins.includes(value)) {
        type = "builtin";
      } else if (/^[{}()[\];,]$/.test(value)) {
        type = "punctuation";
      } else if (/^[+\-*/=<>!&|]+$/.test(value)) {
        type = "operator";
      } else if (/^[a-zA-Z_$]/.test(value)) {
        type = "identifier";
      }
    }

    tokens.push({ type, value });
  }

  return tokens;
}

// --- Main Component ---
export default function AntdCodeEditor({
  initialCode = "",
  language = "javascript",
}) {
  const [code, setCode] = useState(initialCode);
  const preRef = useRef(null);
  const textareaRef = useRef(null);

  // Sync scroll position and handle cursor positioning
  const syncScroll = () => {
    if (preRef.current && textareaRef.current?.resizableTextArea?.textArea) {
      const ta = textareaRef.current.resizableTextArea.textArea;
      preRef.current.scrollTop = ta.scrollTop;
      preRef.current.scrollLeft = ta.scrollLeft;

      // Make overlay exactly match textarea dimensions
      preRef.current.style.width = `${ta.clientWidth}px`;
      preRef.current.style.height = `${ta.clientHeight}px`;
    }
  };

  // Handle clicks on the overlay to position cursor correctly
  const handleOverlayClick = (e) => {
    if (textareaRef.current?.resizableTextArea?.textArea) {
      const textarea = textareaRef.current.resizableTextArea.textArea;
      textarea.focus();

      // Calculate approximate cursor position based on click
      const rect = preRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Updated metrics for Courier New 14px
      const lineHeight = 20; // Fixed 20px line height
      const charWidth = 8.4; // Courier New character width

      const line = Math.floor((y - 12) / lineHeight); // subtract padding
      const col = Math.floor((x - 12) / charWidth); // subtract padding

      const lines = code.split("\n");
      let position = 0;

      for (let i = 0; i < Math.min(line, lines.length - 1); i++) {
        position += lines[i].length + 1; // +1 for newline
      }

      if (line < lines.length) {
        position += Math.min(col, lines[line]?.length || 0);
      }

      textarea.setSelectionRange(position, position);
    }
  };

  useEffect(syncScroll, [code]);

  // Handle initial sizing and resize events
  useEffect(() => {
    const handleResize = () => {
      syncScroll();
    };

    // Initial sync after a short delay to ensure elements are rendered
    const timer = setTimeout(syncScroll, 100);

    // Listen for resize events
    window.addEventListener("resize", handleResize);

    return () => {
      clearTimeout(timer);
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // Clean and normalize the code before tokenizing
  const normalizedCode = code.replace(/[\uFEFF\uFFFD]/g, "");
  const tokens = tokenize(normalizedCode, language);

  // Common styles for both elements - must be identical for perfect alignment
  const commonStyles = {
    fontFamily: "'Courier New', Courier, monospace",
    fontSize: "14px",
    lineHeight: "20px", // Fixed line height in pixels
    padding: "12px",
    margin: 0,
    whiteSpace: "pre",
    wordWrap: "normal",
    tabSize: 2,
    boxSizing: "border-box",
    letterSpacing: "0",
    wordSpacing: "0",
    textRendering: "optimizeSpeed",
    fontVariantLigatures: "none",
  };

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "300px", // Fixed height
        border: "1px solid #d9d9d9",
        borderRadius: "8px",
        overflow: "hidden", // Hide container overflow
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      }}
    >
      {/* Syntax highlighting overlay */}
      <pre
        ref={preRef}
        aria-hidden="true"
        onClick={handleOverlayClick}
        style={{
          ...commonStyles,
          position: "absolute",
          top: 0,
          left: 0,
          pointerEvents: "auto",
          cursor: "text",
          color: "transparent",
          backgroundColor: "transparent",
          overflow: "hidden",
          zIndex: 1,
          border: "none",
          borderRadius: "0",
          outline: "none",
          resize: "none",
          boxSizing: "border-box",
        }}
      >
        {tokens.map((token, i) => (
          <span
            key={i}
            style={{
              ...tokenStyles[token.type],
              whiteSpace: "pre",
            }}
          >
            {token.value}
          </span>
        ))}
      </pre>

      {/* Editable textarea */}
      <TextArea
        ref={textareaRef}
        value={code}
        onChange={(e) => setCode(e.target.value)}
        onScroll={syncScroll}
        onSelect={syncScroll}
        autoSize={false}
        spellCheck={false}
        onKeyDown={(e) => {
          if (e.key === "Tab") {
            e.preventDefault();
            const start = e.target.selectionStart;
            const end = e.target.selectionEnd;
            setCode(
              code.substring(0, start) + "  " + code.substring(end, code.length)
            );
            // Use setTimeout to ensure the state update happens first
            setTimeout(() => {
              e.target.setSelectionRange(start + 2, start + 2);
            }, 0);
          }
        }}
        style={{
          ...commonStyles,
          backgroundColor: "transparent",
          color: "transparent",
          caretColor: "#1890ff",
          resize: "none",
          outline: "none",
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 2,
          border: "none",
          borderRadius: "0",
          boxShadow: "none",
          overflow: "auto", // Allow textarea to scroll
          // Ensure exact alignment
          textIndent: "0px",
          textAlign: "left",
          // Prevent text selection highlighting
          userSelect: "text",
          WebkitUserSelect: "text",
          MozUserSelect: "text",
          msUserSelect: "text",
          boxSizing: "border-box",
        }}
        onFocus={(e) => {
          e.target.parentElement.style.borderColor = "#1890ff";
          e.target.parentElement.style.boxShadow =
            "0 0 0 2px rgba(24, 144, 255, 0.2)";
          syncScroll(); // Sync on focus
        }}
        onBlur={(e) => {
          e.target.parentElement.style.borderColor = "#d9d9d9";
          e.target.parentElement.style.boxShadow =
            "0 2px 8px rgba(0, 0, 0, 0.1)";
        }}
        onResize={syncScroll}
        placeholder="Enter your code here..."
      />
    </div>
  );
}
