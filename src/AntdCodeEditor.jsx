import { useState, useRef, useEffect } from "react";
import { Input } from "antd";
const { TextArea } = Input;

// --- Token Data ---
const KEYWORDS = [
  "const",
  "let",
  "var",
  "function",
  "return",
  "if",
  "else",
  "for",
  "while",
  "do",
  "switch",
  "case",
  "break",
  "continue",
  "new",
  "try",
  "catch",
  "finally",
  "throw",
  "typeof",
  "instanceof",
  "void",
  "delete",
  "in",
  "of",
  "this",
  "super",
  "class",
  "extends",
  "import",
  "export",
  "default",
  "async",
  "await",
  "yield",
];
const LITERALS = ["true", "false", "null", "undefined"];
const BUILT_INS = ["console", "Math", "Date", "Array", "Object", "String"];

const tokenStyles = {
  keyword: { color: "#d73a49", fontWeight: "bold" },
  literal: { color: "#005cc5" },
  builtin: { color: "#6f42c1" },
  string: { color: "#032f62" },
  number: { color: "#005cc5" },
  operator: { color: "#d73a49" },
  identifier: { color: "#24292e" },
  punctuation: { color: "#24292e" },
  comment: { color: "#6a737d", fontStyle: "italic" },
  whitespace: {},
  plain: { color: "#24292e" },
};

function tokenize(code) {
  const regex =
    /\s+|\/\/.*|\/\*[\s\S]*?\*\/|"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\d+(?:\.\d+)?|[a-zA-Z_$][a-zA-Z0-9_$]*|=>|==|!=|<=|>=|[\+\-\*\/=<>!&|]+|[{}()[\];,]/g;

  const tokens = [];
  let match;

  while ((match = regex.exec(code)) !== null) {
    const value = match[0];
    let type = "plain";

    if (/^\s+$/.test(value)) type = "whitespace";
    else if (/^\/\/|\/\*/.test(value)) type = "comment";
    else if (/^['"]/.test(value)) type = "string";
    else if (/^\d/.test(value)) type = "number";
    else if (LITERALS.includes(value)) type = "literal";
    else if (KEYWORDS.includes(value)) type = "keyword";
    else if (BUILT_INS.includes(value)) type = "builtin";
    else if (/^[{}()[\];,]$/.test(value)) type = "punctuation";
    else if (/^[+\-*/=<>!&|]+$/.test(value)) type = "operator";
    else if (/^[a-zA-Z_$]/.test(value)) type = "identifier";

    tokens.push({ type, value });
  }

  return tokens;
}

// --- Main Component ---
export default function AntdCodeEditor({ initialCode = "" }) {
  const [code, setCode] = useState(initialCode);
  const preRef = useRef(null);
  const textareaRef = useRef(null);

  // Sync scroll position and handle cursor positioning
  const syncScroll = () => {
    if (preRef.current && textareaRef.current?.resizableTextArea?.textArea) {
      const ta = textareaRef.current.resizableTextArea.textArea;
      preRef.current.scrollTop = ta.scrollTop;
      preRef.current.scrollLeft = ta.scrollLeft;
    }
  };

  // Handle clicks on the overlay to position cursor correctly
  const handleOverlayClick = (e) => {
    if (textareaRef.current?.resizableTextArea?.textArea) {
      const textarea = textareaRef.current.resizableTextArea.textArea;
      textarea.focus();

      // Calculate approximate cursor position based on click
      const rect = preRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Updated metrics for Courier New 14px
      const lineHeight = 20; // Fixed 20px line height
      const charWidth = 8.4; // Courier New character width

      const line = Math.floor((y - 12) / lineHeight); // subtract padding
      const col = Math.floor((x - 12) / charWidth); // subtract padding

      const lines = code.split("\n");
      let position = 0;

      for (let i = 0; i < Math.min(line, lines.length - 1); i++) {
        position += lines[i].length + 1; // +1 for newline
      }

      if (line < lines.length) {
        position += Math.min(col, lines[line]?.length || 0);
      }

      textarea.setSelectionRange(position, position);
    }
  };

  useEffect(syncScroll, [code]);

  // Clean and normalize the code before tokenizing
  const normalizedCode = code.replace(/[\uFEFF\uFFFD]/g, "");
  const tokens = tokenize(normalizedCode);

  // Common styles for both elements - must be identical for perfect alignment
  const commonStyles = {
    fontFamily: "'Courier New', Courier, monospace",
    fontSize: "14px",
    lineHeight: "20px", // Fixed line height in pixels
    padding: "12px",
    margin: 0,
    whiteSpace: "pre",
    wordWrap: "normal",
    tabSize: 2,
    boxSizing: "border-box",
    letterSpacing: "0",
    wordSpacing: "0",
    textRendering: "optimizeSpeed",
    fontVariantLigatures: "none",
  };

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "300px", // Fixed height
        border: "1px solid #d9d9d9",
        borderRadius: "8px",
        overflow: "auto", // Allow scrolling on the container
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      }}
    >
      {/* Syntax highlighting overlay */}
      <pre
        ref={preRef}
        aria-hidden="true"
        onClick={handleOverlayClick}
        style={{
          ...commonStyles,
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: "auto",
          cursor: "text",
          color: "transparent",
          backgroundColor: "transparent",
          overflow: "hidden",
          zIndex: 1,
          height: "100%",
          width: "100%",
          border: "none",
          borderRadius: "0",
          outline: "none",
          resize: "none",
        }}
      >
        {tokens.map((token, i) => (
          <span
            key={i}
            style={{
              ...tokenStyles[token.type],
              whiteSpace: "pre",
            }}
          >
            {token.value}
          </span>
        ))}
      </pre>

      {/* Editable textarea */}
      <TextArea
        ref={textareaRef}
        value={code}
        onChange={(e) => setCode(e.target.value)}
        onScroll={syncScroll}
        onSelect={syncScroll}
        autoSize={false}
        spellCheck={false}
        style={{
          ...commonStyles,
          backgroundColor: "transparent",
          color: "transparent",
          caretColor: "#1890ff",
          resize: "none",
          outline: "none",
          position: "relative",
          zIndex: 2,
          height: "100%",
          width: "100%",
          border: "none",
          borderRadius: "0",
          boxShadow: "none",
          overflow: "hidden",
          // Ensure exact alignment
          textIndent: "0px",
          textAlign: "left",
          // Prevent text selection highlighting
          userSelect: "text",
          WebkitUserSelect: "text",
          MozUserSelect: "text",
          msUserSelect: "text",
        }}
        onFocus={(e) => {
          e.target.parentElement.style.borderColor = "#1890ff";
          e.target.parentElement.style.boxShadow =
            "0 0 0 2px rgba(24, 144, 255, 0.2)";
        }}
        onBlur={(e) => {
          e.target.parentElement.style.borderColor = "#d9d9d9";
          e.target.parentElement.style.boxShadow =
            "0 2px 8px rgba(0, 0, 0, 0.1)";
        }}
        placeholder="Enter your code here..."
      />
    </div>
  );
}
