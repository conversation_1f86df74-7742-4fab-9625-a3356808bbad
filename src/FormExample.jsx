import { Form, Button, Input, Select, Card, message } from "antd";
import AntdCodeEditor from "./AntdCodeEditor.jsx";

const { Option } = Select;

export default function FormExample({ onBack }) {
  const [form] = Form.useForm();

  const onFinish = (values) => {
    console.log("Form values:", values);
    message.success("Form submitted successfully!");
    message.info("Check console for form values");
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
    message.error("Please check the form fields");
  };

  return (
    <div
      style={{
        padding: "40px 20px",
        maxWidth: "1000px",
        margin: "0 auto",
        backgroundColor: "#f5f5f5",
        minHeight: "100vh",
      }}
    >
      <Card
        title={
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <span>Code Editor in Ant Design Form</span>
            {onBack && <Button onClick={onBack}>← Back to Editor Demo</Button>}
          </div>
        }
        style={{
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
        }}
      >
        <Form
          form={form}
          name="codeForm"
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          initialValues={{
            projectName: "My Project",
            language: "javascript",
            jsCode: `// Default JavaScript code
function greet(name) {
  return \`Hello, \${name}!\`;
}

console.log(greet("World"));`,
            cssCode: `/* Default CSS styles */
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f0f2f5;
}

.title {
  color: #1890ff;
  font-size: 24px;
  font-weight: bold;
}`,
          }}
        >
          <Form.Item
            label="Project Name"
            name="projectName"
            rules={[
              {
                required: true,
                message: "Please input the project name!",
              },
            ]}
          >
            <Input placeholder="Enter project name" />
          </Form.Item>

          <Form.Item
            label="Primary Language"
            name="language"
            rules={[
              {
                required: true,
                message: "Please select a language!",
              },
            ]}
          >
            <Select placeholder="Select language">
              <Option value="javascript">JavaScript</Option>
              <Option value="css">CSS</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="JavaScript Code"
            name="jsCode"
            rules={[
              {
                required: true,
                message: "Please enter some JavaScript code!",
              },
              {
                min: 10,
                message: "Code must be at least 10 characters long!",
              },
            ]}
          >
            <AntdCodeEditor language="javascript" />
          </Form.Item>

          <Form.Item
            label="CSS Code"
            name="cssCode"
            rules={[
              {
                required: true,
                message: "Please enter some CSS code!",
              },
            ]}
          >
            <AntdCodeEditor language="css" />
          </Form.Item>

          <Form.Item>
            <div style={{ display: "flex", gap: "12px" }}>
              <Button type="primary" htmlType="submit">
                Submit Code
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  message.info("Form reset to initial values");
                }}
              >
                Reset Form
              </Button>
              <Button
                type="dashed"
                onClick={() => {
                  const values = form.getFieldsValue();
                  console.log("Current form values:", values);
                  message.info("Current values logged to console");
                }}
              >
                Log Values
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
