import AntdCodeEditor from "./AntdCodeEditor.jsx";

export default function App() {
  const initialCode = `// Editable code editor using
// Ant Design
function add(a, b) {
  return a + b;
}
console.log(add(1,2));
asd
ads
asd
asd`;

  return (
    <div style={{ padding: 20, maxWidth: 800 }}>
      <h2>Editable Code Editor (AntD)</h2>
      <AntdCodeEditor initialCode={initialCode} />
    </div>
  );
}
