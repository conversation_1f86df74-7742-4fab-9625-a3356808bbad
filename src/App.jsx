import { useState } from "react";
import { Button } from "antd";
import AntdCodeEditor from "./AntdCodeEditor.jsx";
import FormExample from "./FormExample.jsx";

export default function App() {
  const [currentView, setCurrentView] = useState("editor");

  const jsCode = `// JavaScript syntax highlighting
function add(a, b) {
  return a + b;
}

const result = add(1, 2);
console.log(result);

// More examples
const array = [1, 2, 3];
const object = { name: "test", value: true };`;

  const cssCode = `/* CSS syntax highlighting */
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 20px;
  margin: 10px auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button {
  background: linear-gradient(45deg, #1890ff, #40a9ff);
  color: white;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}`;

  if (currentView === "form") {
    return <FormExample onBack={() => setCurrentView("editor")} />;
  }

  return (
    <div
      style={{
        padding: "40px 20px",
        maxWidth: "1200px",
        margin: "0 auto",
        backgroundColor: "#f5f5f5",
        minHeight: "100vh",
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          padding: "30px",
          borderRadius: "12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
          marginBottom: "30px",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "24px",
          }}
        >
          <h1
            style={{
              margin: "0",
              color: "#1890ff",
              fontSize: "28px",
              fontWeight: "600",
            }}
          >
            Multi-Language Code Editor (AntD)
          </h1>
          <Button type="primary" onClick={() => setCurrentView("form")}>
            View Form Example
          </Button>
        </div>
        <p
          style={{
            margin: "0 0 24px 0",
            color: "#666",
            fontSize: "16px",
          }}
        >
          A syntax-highlighted code editor with support for JavaScript and CSS
        </p>

        <h3 style={{ margin: "0 0 12px 0", color: "#333" }}>
          JavaScript Editor
        </h3>
        <AntdCodeEditor initialCode={jsCode} language="javascript" />

        <h3 style={{ margin: "30px 0 12px 0", color: "#333" }}>CSS Editor</h3>
        <AntdCodeEditor initialCode={cssCode} language="css" />
      </div>
    </div>
  );
}
