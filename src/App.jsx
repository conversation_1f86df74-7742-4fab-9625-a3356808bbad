import AntdCodeEditor from "./AntdCodeEditor.jsx";

export default function App() {
  const initialCode = `// Editable code editor using
// Ant Design
function add(a, b) {
  return a + b;
}
console.log(add(1,2));
asd
ads
asd
asd`;

  return (
    <div
      style={{
        padding: "40px 20px",
        maxWidth: "900px",
        margin: "0 auto",
        backgroundColor: "#f5f5f5",
        minHeight: "100vh",
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          padding: "30px",
          borderRadius: "12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
        }}
      >
        <h1
          style={{
            margin: "0 0 24px 0",
            color: "#1890ff",
            fontSize: "28px",
            fontWeight: "600",
          }}
        >
          Editable Code Editor (AntD)
        </h1>
        <p
          style={{
            margin: "0 0 24px 0",
            color: "#666",
            fontSize: "16px",
          }}
        >
          A syntax-highlighted code editor built with React and Ant Design
        </p>
        <AntdCodeEditor initialCode={initialCode} />
      </div>
    </div>
  );
}
